c[?7l[2J[0mSeaBIOS (version rel-1.16.3-0-ga6ed6b701f0a-prebuilt.qemu.org)


iPXE (http://ipxe.org) 00:03.0 CA00 PCI2.10 PnP PMM+7EFD1090+7EF31090 CA00
Press Ctrl-B to configure iPXE (PCI 00:03.0)...
                                                                               


Booting from Hard Disk...
FRACTAL OS BOOTLOADER v2.0
Loading kernel...
Loading userland...
Launching kernel...
KERNEL START
SEGMENTS OK
c[?7l[2JSCREEN OK
BEFORE SPEED
[0m=== SPEED CALCULATION DEBUG ===== SPEED CALCULATION DEBUG ===
=
SPEED CALC USING SIZE:SPEED CALC USING SIZE:  16777216 K KB
B
Trying BIOS speed estimation..Trying BIOS speed estimation...
.
BIOS SPEED DX VALUE:BIOS SPEED DX VALUE:  32510 block blocks
s
ESTIMATED: DDR3 1600MHESTIMATED: DDR3 1600MHz
z
BIOS SPEED ESTIMATED:BIOS SPEED ESTIMATED:  1600 MH MHz
AFTER SPEED
