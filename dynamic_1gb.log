c[?7l[2J[0mSea<PERSON><PERSON> (version rel-1.16.3-0-ga6ed6b701f0a-prebuilt.qemu.org)


iPXE (http://ipxe.org) 00:03.0 CA00 PCI2.10 PnP PMM+3EFD1090+3EF31090 CA00
Press Ctrl-B to configure iPXE (PCI 00:03.0)...
                                                                               


Booting from Hard Disk...
FRACTAL OS BOOTLOADER v2.0
Loading kernel...
Loading userland...
Launching kernel...
KERNEL START
SEGMENTS OK
c[?7l[2JSCREEN OK
BEFORE DETECT
[0mKERNEL DYNAMIC STARKERNEL DYNAMIC START
T
KERNEL BIOS CX:KERNEL BIOS CX:  15360 K KB
B
KERNEL BIOS DX:KERNEL BIOS DX:  16126 block blocks
s
KERNEL CALCULATED RAM:KERNEL CALCULATED RAM:  1048448 K KB
B
=== SPEED CALCULATION DEBUG ===== SPEED CALCULATION DEBUG ===
=
SPEED CALC USING SIZE:SPEED CALC USING SIZE:  1048448 K KB
B
Trying BIOS speed estimation..Trying BIOS speed estimation...
.
BIOS SPEED DX VALUE:BIOS SPEED DX VALUE:  16126 block blocks
s
ESTIMATED: DDR3 1333MHESTIMATED: DDR3 1333MHz
z
BIOS SPEED ESTIMATED:BIOS SPEED ESTIMATED:  1333 MH MHz
z
KERNEL FINAL RESULT:KERNEL FINAL RESULT:  1048448 K KB
B
KERNEL DYNAMIC SPEED:KERNEL DYNAMIC SPEED:  1333 MH MHz
AFTER SPEED
