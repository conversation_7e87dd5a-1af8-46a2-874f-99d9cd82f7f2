ASM = nasm
ASM_FLAGS = -f bin -Ox

all: os.img

boot.bin: boot.asm
	$(ASM) $(ASM_FLAGS) boot.asm -o boot.bin

kernel.bin: kernel.asm
	$(ASM) $(ASM_FLAGS) kernel.asm -o kernel.bin

userland.bin: main.asm
	$(ASM) $(ASM_FLAGS) main.asm -o userland.bin

os.img: boot.bin kernel.bin userland.bin
	dd if=/dev/zero of=os.img bs=512 count=2097152
	dd if=boot.bin of=os.img conv=notrunc
	dd if=kernel.bin of=os.img bs=512 seek=2 conv=notrunc
	dd if=userland.bin of=os.img bs=512 seek=66 conv=notrunc

run: os.img
	qemu-system-i386 -drive file=os.img,format=raw -device piix3-usb-uhci -device usb-tablet

clean:
	del -f *.bin *.img