@echo off
echo ========================================
echo RUNNING OS WITH 32GB DDR4 SIMULATION
echo ========================================
echo.

echo This will open QEMU with your OS running.
echo Look for the RAM information on the screen.
echo.
echo Expected results:
echo - RAM Size: ~33554432 KB (32GB)
echo - RAM Type: DDR4
echo - RAM Speed: 3200MHz or 2400MHz
echo.
echo Old buggy results would show:
echo - RAM Size: 4294948864 KB (overflow bug)
echo - RAM Type: DDR3
echo - RAM Speed: 1333MHz
echo.

echo Press any key to start the test...
pause > nul

echo Starting QEMU with 32GB RAM simulation...
qemu-system-i386 -drive format=raw,file=os.img -m 32768

echo.
echo Test completed.
pause
