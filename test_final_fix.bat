@echo off
echo ========================================
echo TESTING FINAL RAM DETECTION FIX
echo ========================================
echo.

echo Testing with different RAM sizes to verify the fix:
echo.

echo Test 1: 16GB RAM (should show ~16777216 KB and DDR4)
qemu-system-i386 -drive format=raw,file=os.img -m 16384 -display none -serial stdio -no-reboot > test_16gb_fixed.log 2>&1 &
timeout /t 5 > nul
taskkill /f /im qemu-system-i386.exe > nul 2>&1

echo Test 2: 32GB RAM (should show ~33554432 KB and DDR4)
qemu-system-i386 -drive format=raw,file=os.img -m 32768 -display none -serial stdio -no-reboot > test_32gb_fixed.log 2>&1 &
timeout /t 5 > nul
taskkill /f /im qemu-system-i386.exe > nul 2>&1

echo.
echo Tests completed. Checking results...
echo.

echo === 16GB Test Results ===
type test_16gb_fixed.log | findstr /i "RAM\|DDR\|KB\|MHz"
echo.

echo === 32GB Test Results ===
type test_32gb_fixed.log | findstr /i "RAM\|DDR\|KB\|MHz"
echo.

echo ========================================
echo ANALYSIS
echo ========================================
echo Expected for 16GB: ~16777216 KB, DDR4 2400-3200MHz
echo Expected for 32GB: ~33554432 KB, DDR4 3200MHz
echo Old bug would show: 4294948864 KB, DDR3 1333MHz
echo.
pause
