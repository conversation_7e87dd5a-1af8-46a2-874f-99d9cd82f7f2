# Fractal OS Information

## Summary
Fractal OS is an operating system project written in assembly language that implements dynamic hardware detection, adaptive memory management, and fractal loading patterns. The system automatically detects hardware configuration and optimizes its behavior accordingly, featuring RAM detection, disk detection, and a fractal loading system that cycles through different processor modes.

## Structure
- **boot.asm**: 512-byte bootloader that loads kernel and userland, enables A20 line
- **kernel.asm**: Main fractal loading engine with hardware detection
- **main.asm**: Isolated userland with dynamic display
- **Makefile**: Build configuration for the system
- **test_*.bat**: Various batch files for testing different aspects of the system
- **test_fix.py**: Python script to verify RAM detection functionality
- **IMAGES/**: Directory containing background images for the OS

## Language & Runtime
**Language**: x86 Assembly (NASM)
**Build System**: Make
**Emulation**: QEMU (i386)

## Architecture
- **Bootloader** (boot.asm): Loads kernel and userland, enables A20 line
- **Kernel** (kernel.asm): Hardware detection, fractal loading engine
- **Userland** (main.asm): User interface with complete error isolation

### Memory Layout
- `0x7C00`: Bootloader
- `0x1000:0x0000`: Kernel segment
- `0x2000:0x0000`: Userland segment
- `0x70000+`: Page tables (for long mode)

## Key Features
- **Hardware Detection**: Automatically detects RAM capacity, type, and speed
- **Fractal Loading System**: Cycles through 6 different processor modes
- **Memory Management**: Adjusts loading amounts based on available RAM
- **User Interface**: Isolated userland with real-time system information display
- **Error Handling**: Complete isolation between userland and kernel operations

## Build & Installation
```bash
# Clean previous build artifacts
make clean

# Build the OS image
make

# Run in QEMU
make run
```

## Manual Build
```bash
nasm -f bin -Ox boot.asm -o boot.bin
nasm -f bin -Ox kernel.asm -o kernel.bin
nasm -f bin -Ox main.asm -o userland.bin
dd if=/dev/zero of=os.img bs=512 count=4096
dd if=boot.bin of=os.img conv=notrunc
dd if=kernel.bin of=os.img bs=512 seek=2 conv=notrunc
dd if=userland.bin of=os.img bs=512 seek=66 conv=notrunc
```

## Testing
The project includes numerous test scripts:
- **test_dynamic_final.bat**: Tests dynamic RAM detection with different memory sizes
- **test_fix.py**: Python script to verify RAM detection functionality
- **test_ram.bat**: Tests basic RAM detection
- Various other test scripts for specific features

### QEMU Testing
```bash
# Run with specific memory size (e.g., 2GB)
qemu-system-i386 -hda os.img -m 2048

# Run with serial output
qemu-system-i386 -hda os.img -display none -serial stdio -no-reboot
```

## System Requirements
- x86-64 compatible processor
- Minimum 512MB RAM (optimized for 1GB+)
- Any storage device (HDD/SSD/NVMe)
- BIOS or UEFI with legacy boot support

## Dynamic Behavior
- **Chunk Size Calculation**: Adjusts based on available RAM (128MB to 2GB chunks)
- **Cycle Calculation**: Dynamically calculates cycles needed based on storage size
- **Spawning Patterns**: Adjusts based on available RAM (conservative to aggressive)
- **Screen Updates**: Real-time display of hardware information and loading progress

## Customization
- Modify `calculate_optimal_chunk` function in kernel.asm to adjust chunk sizes
- Edit main.asm to customize the userland interface
- Extend detection functions in kernel.asm for additional hardware support