c[?7l[2J[0mSea<PERSON><PERSON> (version rel-1.16.3-0-ga6ed6b701f0a-prebuilt.qemu.org)


iPXE (http://ipxe.org) 00:03.0 CA00 PCI2.10 PnP PMM+BEFD1090+BEF31090 CA00
Press Ctrl-B to configure iPXE (PCI 00:03.0)...
                                                                               


Booting from Hard Disk...
FRACTAL OS BOOTLOADER v2.0
Loading kernel...
Loading userland...
Launching kernel...
KERNEL START
SEGMENTS OK
c[?7l[2J[0mΩ
SCREEN OK
BEFORE DETECT
KERNEL DYNAMIC STARKERNEL DYNAMIC START
T
KERNEL BIOS CX:KERNEL BIOS CX:  15360 K KB
B
KERNEL BIOS DX:KERNEL BIOS DX:  48894 block blocks
s
KERNEL CALCULATED RAM:KERNEL CALCULATED RAM:  3145600 K KB
B
=== SPEED CALCULATION DEBUG ===== SPEED CALCULATION DEBUG ===
=
SPEED CALC USING SIZE:SPEED CALC USING SIZE:  3145600 K KB
B
Trying BIOS speed estimation..Trying BIOS speed estimation...
.
BIOS SPEED DX VALUE:BIOS SPEED DX VALUE:  48894 block blocks
s
ESTIMATED: DDR3 1600MHESTIMATED: DDR3 1600MHz
z
BIOS SPEED ESTIMATED:BIOS SPEED ESTIMATED:  1600 MH MHz
z
KERNEL FINAL RESULT:KERNEL FINAL RESULT:  3145600 K KB
B
KERNEL DYNAMIC SPEED:KERNEL DYNAMIC SPEED:  1600 MH MHz
AFTER SPEED
