@echo off
echo ========================================
echo TESTING 32GB DDR4 DETECTION FIX
echo ========================================
echo.

echo Testing with 32GB RAM (simulating your system)...
echo Expected: ~33554432 KB (32GB) and DDR4 3200MHz
echo Old bug: 4294948864 KB (overflow) and DDR3 1333MHz
echo.

echo Running test...
qemu-system-i386 -drive format=raw,file=os.img -m 32768 -display none -serial stdio -no-reboot > test_32gb_result.log 2>&1 &

echo Waiting for test to complete...
timeout /t 8 > nul

echo Stopping QEMU...
taskkill /f /im qemu-system-i386.exe > nul 2>&1

echo.
echo ========================================
echo TEST RESULTS
echo ========================================

if exist test_32gb_result.log (
    echo RAM and DDR information from test:
    type test_32gb_result.log | findstr /i "RAM\|DDR\|KB\|MHz\|SPEED\|TYPE"
    echo.
    echo Full log saved to: test_32gb_result.log
) else (
    echo No log file created - test may have failed
)

echo.
echo ========================================
echo ANALYSIS
echo ========================================
echo If you see ~33554432 KB and DDR4, the fix is working!
echo If you see 4294948864 KB and DDR3, the bug is still present.
echo.
pause
